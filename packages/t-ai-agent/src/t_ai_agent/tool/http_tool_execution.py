from typing import Any, Dict, Optional
from pydantic import BaseModel, ConfigDict, Field


from loguru import logger
from .tool_call_execution import Tool<PERSON>allExecution
from ..model import AgentExecutionContext
from ..model.agent_dsl import FieldMeta, HttpTool
from ..utils.json_util import safe_json_loads
from ..utils.trantor_helper import dynamic_value


class ToolCallExecutionModel(BaseModel):
    model_config = ConfigDict(
        populate_by_name=True,
        validate_default=True,
        extra="ignore",
        validate_assignment=True,
    )


class HttpToolInvokeParams(ToolCallExecutionModel):
    headers: Optional[dict[str, Any]]
    parameter: Optional[dict[str, Any]]
    body: Optional[dict[str, Any]]


class HttpToolExecution(ToolCallExecution):
    """HTTP工具执行类"""
    
    def __init__(self, http_tool: HttpTool):
        self.http_tool = http_tool
    
    def build_tool(self) -> Dict[str, Any]:
        pass
        # return FunctionTool(
        #     name=get_short_key(tool_config.key),
        #     description=tool_config.desc or tool_config.name,
        #     params_json_schema=await convert_to_params_json_schema(build_http_tool_json_schema(tool_config)),
        #     on_invoke_tool=create_http_run_function(tool_config, agent_meta.name),
        #     strict_json_schema=True,
        # )


    def build_tool_json_schema(self) -> Dict[str, Any]:
        """构建HTTP工具的JSON Schema"""
        # 构建工具基本信息
        tool_schema = {
            "type": "function",
            "function": {
                "name": self.http_tool.key,
                "description": self.http_tool.name,
                "parameters": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            }
        }

        # 构建参数schema
        json_schema = build_http_tool_json_schema(self.http_tool)

        # 将FieldMeta转换为JSON Schema格式
        properties = {}
        required = []

        for field_meta in json_schema:
            field_key = field_meta.field_key
            properties[field_key] = {
                "type": field_meta.field_type.lower(),
                "description": field_meta.description or ""
            }

            if field_meta.required:
                required.append(field_key)

            # 处理嵌套字段
            if field_meta.elements:
                properties[field_key]["properties"] = {}
                properties[field_key]["required"] = []

                for element in field_meta.elements:
                    properties[field_key]["properties"][element.field_key] = {
                        "type": element.field_type.lower(),
                        "description": element.description or ""
                    }

                    if element.required:
                        properties[field_key]["required"].append(element.field_key)

        tool_schema["function"]["parameters"]["properties"] = properties
        tool_schema["function"]["parameters"]["required"] = required

        return tool_schema

    def execute(self, params: Dict[str, Any]) -> Any:
        """执行HTTP工具调用"""
        # 这里应该实现实际的HTTP请求逻辑
        # 由于这是一个示例，我们只返回一个占位符
        return {
            "tool": self.http_tool.key,
            "params": params,
            "status": "executed"
        }


def build_http_tool_json_schema(http_tool: HttpTool) -> list[FieldMeta]:
    """构建HTTP工具的JSON Schema"""
    json_schema = []

    # 构建header部分的schema
    header_schema = build_header_json_schema(http_tool)
    if header_schema:
        json_schema.append(header_schema)

    # 构建parameter部分的schema
    param_schema = build_parameter_json_schema(http_tool)
    if param_schema:
        json_schema.append(param_schema)

    # 构建body部分的schema
    body_schema = build_body_json_schema(http_tool)
    if body_schema:
        json_schema.append(body_schema)

    return json_schema


def build_header_json_schema(http_tool: HttpTool) -> Optional[FieldMeta]:
    """构建HTTP请求头的JSON Schema"""
    if not http_tool.headers:
        return None

    # 创建子字段
    elements = []
    for header in http_tool.headers:
        element = FieldMeta(
            fieldKey=header.get("key", ""),
            fieldName=header.get("key", ""),
            fieldType=header.get("value", {}).get("fieldType", "Text"),
            defaultValue=header.get("value", None),
            required=False
        )
        elements.append(element)

    header_field = FieldMeta(
        fieldKey="headers",
        fieldName="headers",
        fieldType="Object",
        description="http request headers",
        elements=elements
    )
    return header_field


def build_parameter_json_schema(http_tool: HttpTool) -> Optional[FieldMeta]:
    """构建HTTP请求参数的JSON Schema"""
    if not http_tool.params:
        return None

    # 创建子字段
    elements = []
    for param in http_tool.params:
        element = FieldMeta(
            fieldKey=param.get("key", ""),
            fieldName=param.get("key", ""),
            fieldType=param.get("value", {}).get("fieldType", "Text"),
            defaultValue=param.get("value", None),
            required=False
        )
        elements.append(element)

    param_field = FieldMeta(
        fieldKey="parameter",
        fieldName="parameter",
        fieldType="Object",
        description="http request parameter",
        elements=elements
    )
    return param_field


def build_body_json_schema(http_tool: HttpTool) -> Optional[FieldMeta]:
    """构建HTTP请求体的JSON Schema"""
    if not http_tool.json_body:
        return None

    if http_tool.body_type == "VALUE":
        return FieldMeta(
            fieldKey="body",
            fieldName="body",
            fieldType="Object",
            description="http request body",
            defaultValue=http_tool.body
        )
    else:
        # 处理jsonBody字段
        elements = []
        if isinstance(http_tool.json_body, list):
            for item in http_tool.json_body:
                elements.append(item)

        body_field = FieldMeta(
            fieldKey="body",
            fieldName="body",
            fieldType="Object",
            description="http request body",
            elements=elements
        )
        return body_field


async def get_http_headers(agent_exec_ctx: AgentExecutionContext, tool_config: HttpTool, http_tool_invoke_params: HttpToolInvokeParams):
    http_headers = {}
    if http_tool_invoke_params.headers is not None:
        http_headers = http_tool_invoke_params.headers
    for header in tool_config.headers or []:
        header_value = await dynamic_value(header.get("value", {}), agent_exec_ctx.variables)
        http_headers.update({header.get("key", ""): header_value})
    logger.debug(f"HTTP Headers: {http_headers}")
    return http_headers


async def get_http_parameter(agent_exec_ctx: AgentExecutionContext, tool_config: HttpTool, http_tool_invoke_params: HttpToolInvokeParams):
    http_params = {}
    if http_tool_invoke_params.parameter is not None:
        http_params = http_tool_invoke_params.parameter
    for params in tool_config.params or []:
        params_value = await dynamic_value(params.get("value", {}), agent_exec_ctx.variables)
        http_params.update({params.get("key", ""): params_value})
    logger.debug(f"HTTP Params: {http_params}")
    return http_params


async def get_http_path_variables(agent_exec_ctx: AgentExecutionContext, tool_config: HttpTool, http_tool_invoke_params: HttpToolInvokeParams):
    http_path_variables = {}
    for path_variables in tool_config.path_variables or []:
        path_variables_value = await dynamic_value(path_variables.get("value", {}), agent_exec_ctx.variables)
        http_path_variables.update({path_variables.get("key", ""): path_variables_value})
    logger.debug(f"HTTP Path Variables: {http_path_variables}")
    return http_path_variables


async def get_http_body(agent_exec_ctx: AgentExecutionContext, tool_config: HttpTool, http_tool_invoke_params: HttpToolInvokeParams):
    http_body = {}
    if tool_config.body_type == 'JSON':
        if http_tool_invoke_params.body is not None:
            http_body = http_tool_invoke_params.body
            # 填充覆盖默认值(当前只支持一层)
            await fill_body_field_default_value(agent_exec_ctx, http_body, tool_config.json_body)
        else:
            if tool_config.json_body is not None:
                if isinstance(tool_config.json_body, dict):
                    for item in tool_config.json_body:
                        http_body[item.get("key", "")] = await dynamic_value(item.get("value", {}),
                                                                             agent_exec_ctx.variables)
                elif isinstance(tool_config.json_body, str):
                    http_body = safe_json_loads(tool_config.json_body)
    else:
        http_body = await dynamic_value(tool_config.body, agent_exec_ctx.variables)
    logger.debug(f"HTTP Body: {http_body}")
    return http_body


async def fill_body_field_default_value(agent_exec_ctx: AgentExecutionContext, http_body: dict, body_fields: list[FieldMeta]):
    for item in body_fields:
        filed_key = item.field_key
        filed_value = item.default_value
        if filed_key and filed_value and (http_body.get(filed_key) is not None or http_body.get(filed_key) == ""):
            field_default_value = await dynamic_value(filed_value, agent_exec_ctx.variables)
            http_body[filed_key] = field_default_value
            logger.info(f"fill body field: {filed_key} = {field_default_value}")
